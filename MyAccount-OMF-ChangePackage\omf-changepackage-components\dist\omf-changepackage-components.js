/*! omf-changepackage-components (lib) 0.1.0 | bwtk 6.1.0 | 2025-08-25T15:26:41.450Z */
var root,factory;root=self,factory=(e,t,r,n,o,i,a,c)=>(()=>{"use strict";function s(e){var t,r=Mt[e];return void 0!==r?r.exports:(t=Mt[e]={exports:{}},Lt[e](t,t.exports,s),t.exports)}function u(e,t){function r(){this.constructor=e}if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Q(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}function l(e,t){var r,n,o={};for(r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(o[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(n=0,r=Object.getOwnPropertySymbols(e);n<r.length;n++)t.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(o[r[n]]=e[r[n]]);return o}function p(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function d(e,t){var r,n,o,i,a="function"==typeof Symbol&&e[Symbol.iterator];if(!a)return e;r=a.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(n=r.next()).done;)o.push(n.value)}catch(c){i={error:c}}finally{try{n&&!n.done&&(a=r.return)&&a.call(r)}finally{if(i)throw i.error}}return o}function f(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}function h(e){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(e)}function m(e,t){var r,n=Object.keys(e);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(e),t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)),n}function g(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(e,t,r,n){n.debug;var o=function(e){var t,r;for(t=1;t<arguments.length;t++)r=null!=arguments[t]?arguments[t]:{},t%2?m(r,!0).forEach(function(t){g(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(r).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))});return e}({},r);return e&&"object"===h(e)&&Object.keys(e).forEach(function(n){"_persist"!==n&&t[n]===r[n]&&(o[n]=e[n])}),o}function b(e){return JSON.stringify(e)}function E(e){var t,r=e.transforms||[],n="".concat(void 0!==e.keyPrefix?e.keyPrefix:ne).concat(e.key),o=e.storage;return e.debug,t=!1===e.deserialize?function(e){return e}:"function"==typeof e.deserialize?e.deserialize:O,o.getItem(n).then(function(e){var n,o;if(e)try{return n={},o=t(e),Object.keys(o).forEach(function(e){n[e]=r.reduceRight(function(t,r){return r.out(t,e,o)},t(o[e]))}),n}catch(i){throw i}})}function O(e){return JSON.parse(e)}function T(e){}function x(e,t){var r,n=Object.keys(e);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(e),t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)),n}function I(e){var t,r;for(t=1;t<arguments.length;t++)r=null!=arguments[t]?arguments[t]:{},t%2?x(r,!0).forEach(function(t){v(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):x(r).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))});return e}function v(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function N(e){return function(e){if(Array.isArray(e)){for(var t=0,r=new Array(e.length);t<e.length;t++)r[t]=e[t];return r}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function S(e,t){var r,n=Object.keys(e);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(e),t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)),n}function R(e){var t,r;for(t=1;t<arguments.length;t++)r=null!=arguments[t]?arguments[t]:{},t%2?S(r,!0).forEach(function(t){A(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):S(r).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))});return e}function A(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function P(e,t,r){var n,o,i,a;if(!Boolean(t))return e||r;for(n=/^\?/.test(t||""),o=(t||"").replace("?","").split("."),i=e,a=0;a<o.length;a++)i=i&&void 0!==i[o[a]]&&null!==i[o[a]]&&(n||0!==i[o[a]]&&""!==i[o[a]])?i[o[a]]:r;return i}function j(e,t){var r={};if(Object.assign(r,ye,"string"==typeof t?{error:t}:t),void 0===e||r.failNullValue&&null===e||r.failZeroValue&&0===e||r.failEmptyString&&""===e||r.test(e))throw r.error;return e}function C(e,t){void 0===t&&(t=!0);var r=[];return Boolean(e)&&(be=Array.isArray(e)?e:[e]),Array.isArray(be)&&be.length&&r.push(Z.raiseRestriction(be.pop())),t&&r.push(Z.setWidgetStatus(Ne.RENDERED)),r}function w(e){var t,r,n,o=[];for(t=1;t<arguments.length;t++)o[t-1]=arguments[t];switch(j(e,"Expected response object, but got undefined/null"),j(o,"Expected Array<ObservableInput>, but got undefined/null"),sessionStorage.setItem("omf:Initilized","yes"),!0){case!(e.data&&"object"==typeof e.data):return[Z.errorOccured(new je.ErrorHandler(e.statusText,ee(ee({},"object"==typeof e.data?e.data:{}),{url:e.url}))),Z.setWidgetStatus(Ne.RENDERED)];case!!P(e,"data.restriction",!1):return C(P(e,"data.restriction"));default:return r=C(P(e,"data.productOfferingDetail.restriction"),!1),n=[Z.broadcastUpdate(Z.setProductConfigurationTotal(P(e,"data.productOfferingDetail.productConfigurationTotal")))],re.concat.apply(void 0,f([re.of.apply(void 0,f([],d(r),!1)),re.of.apply(void 0,f([],d(n),!1))],d(o),!1))}}function _(e){return t=function(){return new Promise(function(t){setTimeout(function(){return t({data:e})},350)})},new re.Observable(function(e){t().then(function(t){e.next(t)}).catch(function(t){e.error(t)}).then(function(){e.complete()})});var t}function D(e){return(e||"").split("/").pop()}function k(e){return function(t){return(0,Ee.jsx)(Le,{children:function(r){return(0,Ee.jsx)(e,ee({},t,r))}})}}function L(e){var t,r;return{localization:e.createReducer(),widgetStatus:(0,X.handleActions)((t={},t[rt]=function(e,t){return t.payload},t),Ne.UPDATING),error:(0,X.handleActions)((r={},r[nt]=function(e,t){return t.payload},r),null)}}function M(){var e;return{restriction:(0,X.handleActions)((e={},e[it]=function(e,t){return t.payload||e},e[at]=function(){return null},e[ct]=function(){return null},e),null)}}function B(){var e;return{lightboxData:(0,X.handleActions)((e={},e[lt]=function(e,t){return t.payload},e[ut]=function(){return null},e),null)}}function V(e){switch(!0){case 0===e:return"btn btn-primary fill-xs";case 1===e:return"btn btn-default fill-xs";default:return"btn btn-link"}}function H(e){switch(e){case"Error":return(0,Ee.jsx)("span",{className:"icon2 icon-alert-circled txtSize38 txtRed pad-15-right"});case"Information":case"Warning":return(0,Ee.jsx)("span",{className:"icon2 icon-alert-circled txtSize38 txtYellow pad-15-right"});default:return null}}function F(e){return e?1===e.length?"".concat(e,"0"):e.slice(0,2):"00"}function G(e,t){var r,n,o,i,a,c,s;try{switch(n=(r=String(t).split("."))[0],o=r[1],i=t<0,n=n.replace("-",""),Number(n)>0&&(!o||Number(o)),e.locale){case"en":return a=parseInt(n).toLocaleString("en"),i?"CR $".concat(a,".").concat(F(o)):"$".concat(a,".").concat(F(o));case"fr":return c=parseInt(n).toLocaleString("fr"),i?"CR ".concat(c,",").concat(F(o),"&nbsp;$"):"".concat(c,",").concat(F(o),"&nbsp;$");default:return s=parseInt(n).toLocaleString("en"),i?"CR $".concat(s,".").concat(F(o)):"$".concat(s,".").concat(F(o))}}catch(u){return t}}function W(e){return W="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},W(e)}function U(e){return U=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},U(e)}function z(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function q(e,t){return q=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},q(e,t)}function Y(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var K,J,X,Z,Q,ee,te,re,ne,oe,ie,ae,ce,se,ue,le,pe,de,fe,he,me,ge,ye,be,Ee,Oe,Te,xe,Ie,ve,Ne,Se,Re,Ae,Pe,je,Ce,we,_e,De,ke,Le,Me,Be,Ve,He,Fe,Ge,We,Ue,ze,$e,qe,Ye,Ke,Je,Xe,Ze,Qe,et,tt,rt,nt,ot,it,at,ct,st,ut,lt,pt,dt,ft,ht,mt,gt,yt,bt,Et,Ot,Tt,xt,It,vt,Nt,St,Rt,At,Pt,jt,Ct,wt,_t,Dt,kt,Lt={23:(e,t,r)=>{var n,o;t.A=void 0,n=(0,((o=r(752))&&o.__esModule?o:{default:o}).default)("session"),t.A=n},102:t=>{t.exports=e},266:(e,t)=>{function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function n(){}t.__esModule=!0,t.default=function(e){var t="".concat(e,"Storage");return function(e){var t,n;if("object"!==("undefined"==typeof self?"undefined":r(self))||!(e in self))return!1;try{t=self[e],n="redux-persist ".concat(e," test"),t.setItem(n,"test"),t.getItem(n),t.removeItem(n)}catch(o){return!1}return!0}(t)?self[t]:o};var o={getItem:n,setItem:n,removeItem:n}},398:(e,t)=>{t.A=function(e){return e}},418:e=>{e.exports=r},419:e=>{e.exports=o},442:e=>{e.exports=i},493:(e,t,r)=>{e.exports=r(557)},541:e=>{e.exports=t},557:(e,t)=>{function r(e,t,r){var o,i=null;if(void 0!==r&&(i=""+r),void 0!==t.key&&(i=""+t.key),"key"in t)for(o in r={},t)"key"!==o&&(r[o]=t[o]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:i,ref:void 0!==t?t:null,props:r}}
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var n=Symbol.for("react.transitional.element"),o=Symbol.for("react.fragment");t.Fragment=o,t.jsx=r,t.jsxs=r},750:e=>{e.exports=n},752:(e,t,r)=>{t.__esModule=!0,t.default=function(e){var t=(0,o.default)(e);return{getItem:function(e){return new Promise(function(r,n){r(t.getItem(e))})},setItem:function(e,r){return new Promise(function(n,o){n(t.setItem(e,r))})},removeItem:function(e){return new Promise(function(r,n){r(t.removeItem(e))})}}};var n,o=(n=r(266))&&n.__esModule?n:{default:n}},769:e=>{e.exports=a},999:e=>{e.exports=c}},Mt={};s.d=(e,t)=>{for(var r in t)s.o(t,r)&&!s.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},K={},s.r(K),s.d(K,{Actions:()=>Z,Assert:()=>j,BaseClient:()=>we,Components:()=>kt,Context:()=>Le,ContextProvider:()=>ke,EFlowType:()=>Pe,EModals:()=>ve,EReviewMode:()=>Re,EWidgetName:()=>Ae,EWidgetRoute:()=>Se,EWidgetStatus:()=>Ne,FilterRestrictionObservable:()=>w,FormattedHTMLMessage:()=>Te,LifecycleEpics:()=>Ge,ModalEpics:()=>et,Models:()=>je,Omniture:()=>mt,Reducers:()=>pt,RestricitonsEpics:()=>Je,Utils:()=>xe,ValueOf:()=>P,Volt:()=>Ie,WidgetContext:()=>De,withContext:()=>k}),J=s(102),X=s(541),function(e){e.setWidgetStatus=(0,X.createAction)("SET_WIDGET_STATUS"),e.setWidgetProps=(0,X.createAction)("SET_WIDGET_PROPS"),e.getData=(0,X.createAction)("GET_WIDGET_DATA"),e.showHideLoader=(0,X.createAction)("SHOW_HIDE_LOADER"),e.errorOccured=(0,X.createAction)("ERROR_OCCURED"),e.openLightbox=(0,X.createAction)("OPEN_LIGHTBOX"),e.closeLightbox=(0,X.createAction)("CLOSE_LIGHTBOX"),e.setlightboxData=(0,X.createAction)("SET_LIGHTBOX_DATA"),e.broadcastUpdate=(0,X.createAction)("PIPE_SEND_UPDATE",function(e,t){return void 0===t&&(t=0),setTimeout(function(){return J.ServiceLocator.instance.getService(J.CommonServices.EventStream).send(e.type,e.payload)},t),e}),e.refreshTotals=(0,X.createAction)("REFRESH_TOTALS"),e.toggleTVCategoriesTray=(0,X.createAction)("TOGGLE_TV_CATEGORIES_TRAY"),e.setProductConfigurationTotal=(0,X.createAction)("SET_PRODUCT_CONFIGURATION_TOTAL"),e.continueFlow=(0,X.createAction)("FLOW_CONTINUE"),e.handleNav=(0,X.createAction)("HANDLE_NAV"),e.onContinue=(0,X.createAction)("HISTORY_ON_CONTINUE"),e.historyGo=(0,X.createAction)("HISTORY_GO"),e.historyBack=(0,X.createAction)("HISTORY_BACK"),e.historyForward=(0,X.createAction)("HISTORY_FORWARD"),e.applicationReset=(0,X.createAction)("APPLICATION_RESET"),e.applicationExit=(0,X.createAction)("APPLICATION_EXIT"),e.applicationLogout=(0,X.createAction)("APPLICATION_LOGOUT"),e.setHistoryProvider=(0,X.createAction)("SET_HISTORY_PROVIDER"),e.setAppointmentVisited=(0,X.createAction)("APPOINTMENT_PAGE_VISITED"),e.widgetRenderComplete=(0,X.createAction)("WIDGET_RENDER_COMPLETE"),e.raiseRestriction=(0,X.createAction)("RESTRICTION_OCCURRED"),e.acceptRestriction=(0,X.createAction)("RESTRICTION_ACCEPTED"),e.declineRestriction=(0,X.createAction)("RESTRICTION_DECLINED"),e.finalizeRestriction=(0,X.createAction)("RESTRICTION_CYCLE_COMPLETE"),e.clearCachedState=(0,X.createAction)("CLEAR_CACHED_STATE"),e.omniPageLoaded=(0,X.createAction)("OMNITURE_PAGE_LOADED",function(e,t){return e?{name:e,data:t}:void 0}),e.omniPageSubmit=(0,X.createAction)("OMNITURE_PAGE_SUBMIT"),e.omniModalOpen=(0,X.createAction)("OMNITURE_MODAL_OPEN")}(Z||(Z={})),Q=function(e,t){return Q=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},Q(e,t)},ee=function(){return ee=Object.assign||function(e){var t,r,n,o;for(r=1,n=arguments.length;r<n;r++)for(o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},ee.apply(this,arguments)},Object.create,Object.create,te=function(e){return te=Object.getOwnPropertyNames||function(e){var t,r=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[r.length]=t);return r},te(e)},"function"==typeof SuppressedError&&SuppressedError,re=s(418),ne="persist:",oe="persist/FLUSH",ie="persist/REHYDRATE",ae="persist/PAUSE",ce="persist/PERSIST",se="persist/PURGE",ue="persist/REGISTER",le=-1,pe=5e3,de=s(750),fe={registry:[],bootstrapped:!1},he=function(){var e,t,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:fe,n=arguments.length>1?arguments[1]:void 0;switch(n.type){case ue:return R({},r,{registry:[].concat(N(r.registry),[n.key])});case ie:return e=r.registry.indexOf(n.key),(t=N(r.registry)).splice(e,1),R({},r,{registry:t,bootstrapped:0===t.length});default:return r}},me=s(398),ge=s(23),ye={error:"ReferenceError: test is not defined",failEmptyString:!0,failNullValue:!0,failZeroValue:!1,test:function(){return!1}},be=[],Ee=s(493),Oe=s(419),Te=function(e){var t,r,n,o=e.id,i=e.values,a=void 0===i?{}:i,c=e.defaultMessage,s=(0,Oe.useIntl)();try{return t=s.formatMessage({id:o,defaultMessage:c},a),r=/\{(\w+),\s*number,\s*CAD\}/g,(t=t.replace(r,function(e,t){var r=a[t];return null==r||isNaN(r)?"":s.formatNumber(r,{style:"currency",currency:"CAD"})})).includes("{")&&t.includes("}")&&a&&Object.entries(a).forEach(function(e){var r=d(e,2),n=r[0],o=r[1],i="{".concat(n,"}");t=t.replace(new RegExp(i.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"g"),String(o))}),(0,Ee.jsx)("span",{dangerouslySetInnerHTML:{__html:t}})}catch(u){return n=s.messages[o]||c||o,(0,Ee.jsx)("span",{dangerouslySetInnerHTML:{__html:n}})}},function(e){function t(e){return P($("#"+e).data("bs.modal"),"_isShown",!1)}function r(){var e,t=sessionStorage.getItem("omf:Flowtype");if(!t)switch(e=window.location.pathname,!0){case e.indexOf("Changepackage/Internet")>0:t=Pe.INTERNET;break;case e.indexOf("Changepackage/TV")>0:t=Pe.TV;break;case e.indexOf("Add/TV")>0:t=Pe.ADDTV;break;case e.indexOf("Bundle/")>0:t=Pe.BUNDLE}return t}e.getCurrentLocale=function(e){var t=e.locale.substr(0,2);return ee(ee({},e),{formats:e.formats[t],messages:e.messages[t]})},e.showLightbox=function(e){$("#"+e).modal("show")},e.isLightboxOpen=t,e.hideLightbox=function(e){t(e)&&$("#"+e).modal("hide")},e.getCookie=function(e){var t,r,n=e+"=",o=decodeURIComponent(document.cookie).split(";");for(t=0;t<o.length;t++){for(r=o[t];" "===r.charAt(0);)r=r.substring(1);if(0===r.indexOf(n))return r.substring(n.length,r.length)}return""},e.isIOS=/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream,e.isIE11=!!window.MSInputMethodContext&&!!document.documentMode,e.deviceType=function(){var e=$(window).outerWidth();return{isMobile:!(e>767),isTablet:!(e>991)}},e.debounce=function(e,t,r){var n;return function(){var o=this,i=arguments,a=r&&!n;clearTimeout(n),n=setTimeout(function(){n=null,r||e.apply(o,i)},t),a&&e.apply(o,i)}},e.reducer=function(e,t){return function(e,t){var r,n,o,i,a,c,s,u;return r=void 0!==e.version?e.version:le,e.debug,n=void 0===e.stateReconciler?y:e.stateReconciler,o=e.getStoredState||E,i=void 0!==e.timeout?e.timeout:pe,a=null,c=!1,s=!0,u=function(e){return e._persist.rehydrated&&a&&!s&&a.update(e),e},function(l,p){var d,f,h,m,g,y,E=l||{},O=E._persist,x=function(e,t){var r,n,o,i;if(null==e)return{};if(r=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t),Object.getOwnPropertySymbols)for(i=Object.getOwnPropertySymbols(e),o=0;o<i.length;o++)n=i[o],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n]);return r}(E,["_persist"]);if(p.type===ce){if(d=!1,f=function(t,r){d||(p.rehydrate(e.key,t,r),d=!0)},i&&setTimeout(function(){!d&&f(void 0,new Error('redux-persist: persist timed out for persist key "'.concat(e.key,'"')))},i),s=!1,a||(a=function(e){function t(){var e,t;if(0===s.length)return u&&clearInterval(u),void(u=null);if(e=s.shift(),void 0!==(t=f.reduce(function(t,r){return r.in(t,e,a)},a[e])))try{c[e]=o(t)}catch(r){}else delete c[e];0===s.length&&(Object.keys(c).forEach(function(e){void 0===a[e]&&delete c[e]}),l=g.setItem(m,o(c)).catch(n))}function r(e){return!(d&&-1===d.indexOf(e)&&"_persist"!==e||p&&-1!==p.indexOf(e))}function n(e){i&&i(e)}var o,i,a,c,s,u,l,p=e.blacklist||null,d=e.whitelist||null,f=e.transforms||[],h=e.throttle||0,m="".concat(void 0!==e.keyPrefix?e.keyPrefix:ne).concat(e.key),g=e.storage;return o=!1===e.serialize?function(e){return e}:"function"==typeof e.serialize?e.serialize:b,i=e.writeFailHandler||null,a={},c={},s=[],u=null,l=null,{update:function(e){Object.keys(e).forEach(function(t){r(t)&&a[t]!==e[t]&&-1===s.indexOf(t)&&s.push(t)}),Object.keys(a).forEach(function(t){void 0===e[t]&&r(t)&&-1===s.indexOf(t)&&void 0!==a[t]&&s.push(t)}),null===u&&(u=setInterval(t,h)),a=e},flush:function(){for(;0!==s.length;)t();return l||Promise.resolve()}}}(e)),O)return I({},t(x,p),{_persist:O});if("function"!=typeof p.rehydrate||"function"!=typeof p.register)throw new Error("redux-persist: either rehydrate or register is not a function on the PERSIST action. This can happen if the action is being replayed. This is an unexplored use case, please open an issue and we will figure out a resolution.");return p.register(e.key),o(e).then(function(t){(e.migrate||function(e,t){return Promise.resolve(e)})(t,r).then(function(e){f(e)},function(e){f(void 0,e)})},function(e){f(void 0,e)}),I({},t(x,p),{_persist:{version:r,rehydrated:!1}})}if(p.type===se)return c=!0,p.result(function(e){var t=e.storage,r="".concat(void 0!==e.keyPrefix?e.keyPrefix:ne).concat(e.key);return t.removeItem(r,T)}(e)),I({},t(x,p),{_persist:O});if(p.type===oe)return p.result(a&&a.flush()),I({},t(x,p),{_persist:O});if(p.type===ae)s=!0;else if(p.type===ie){if(c)return I({},x,{_persist:I({},O,{rehydrated:!0})});if(p.key===e.key)return h=t(x,p),m=p.payload,g=I({},!1!==n&&void 0!==m?n(m,l,h,e):h,{_persist:I({},O,{rehydrated:!0})}),u(g)}return O?(y=t(x,p))===x?l:u(I({},y,{_persist:O})):t(l,p)}}(e,t)},e.persistStateExists=function(e){return Boolean(localStorage.getItem("omf:".concat(e.key)))},e.clearCachedState=function(e){e.forEach(function(e){return sessionStorage.removeItem("omf:".concat(e))})},e.persistConfig=function(e,t){return void 0===t&&(t=[]),{version:1,keyPrefix:"omf:",storage:ge.A,stateReconciler:me.A,key:e,blacklist:f(["localization","widgetStatus","error","lightboxData","restriction"],d(t),!1)}},e.getFlowType=r,e.constructPageRoute=function(e,t){switch(t||r()){case Pe.INTERNET:switch(e){case Se.INTERNET:return"/Changepackage/Internet";case Se.APPOINTMENT:return"/Changepackage/Internet/Appointment";case Se.REVIEW:return"/Changepackage/Internet/Review";case Se.CONFIRMATION:return"/Changepackage/Internet/Confirmation";default:return"/Changepackage"}case Pe.TV:switch(e){case Se.TV:return"/Changepackage/TV";case Se.APPOINTMENT:return"/Changepackage/TV/Appointment";case Se.REVIEW:return"/Changepackage/TV/Review";case Se.CONFIRMATION:return"/Changepackage/TV/Confirmation";default:return"/Changepackage"}case Pe.ADDTV:switch(e){case Se.TV:return"/Add/TV";case Se.REVIEW:return"/Add/TV/Review";case Se.CONFIRMATION:return"/Add/TV/Confirmation";default:return"/Add"}case Pe.BUNDLE:switch(e){case Se.INTERNET:return"/Bundle/Internet";case Se.TV:return"/Bundle/TV";case Se.APPOINTMENT:return"/Bundle/Internet/Appointment";case Se.REVIEW:return"/Bundle/Review";case Se.CONFIRMATION:return"/Bundle/Confirmation";default:return"/Bundle"}default:return"/"}},e.getPageRoute=function(){var e=window.location.pathname;switch(!0){case e.indexOf("Review")>0:return Se.REVIEW;case e.indexOf("Confirm")>0:return Se.CONFIRMATION;case e.indexOf("Appoint")>0:return Se.APPOINTMENT;case e.indexOf("Internet")>0:return Se.INTERNET;case e.indexOf("TV")>0:return Se.TV}},e.getURLByFlowType=function(e){return e[sessionStorage.getItem("omf:Flowtype")||"Internet"]},e.appendRefreshOnce=function(e){return(e||"")+(sessionStorage.getItem("omf:Initilized")?"":"?refreshCache=true")}}(xe||(xe={})),function(e){var t,r,n,o,i,a,c,s;(s=e.EOfferingState||(e.EOfferingState={})).Add="add",s.Delete="delete",s.Modify="modify",s.NoCharge="noChange",s.Remove="Remove",s.Change="Change",s.InitiallySelected="InitiallySelected",s.NewlySelected="NewlySelected",s.Removed="removed",s.NotSelected="NotSelected",s.Added="Added",s.UnSelected="UnSelected",s.Create="Create",s.NoChange="NoChange",(c=e.EOfferingType||(e.EOfferingType={})).BaseOffering="BaseOffering",c.GroupOffering="GroupOffering",c.SingleOffering="SingleOffering",(a=e.EDIsplayGroupKey||(e.EDIsplayGroupKey={})).TV_BASE_PRODUCT="TV_BASE_PRODUCT",a.ALACARTE="ALACARTE",a.MOVIE="MOVIE",a.TV="TV",a.SPECIALITY_SPORTS="SPECIALITY_SPORTS",a.ADD_ON="ADD_ON",a.INTERNATIONAL="INTERNATIONAL",a.INTERNATIONAL_COMBOS="INTERNATIONAL_COMBOS",a.INTERNATIONAL_ALACARTE="INTERNATIONAL_ALACARTE",a.BASE_PROGRAMMING="BASE_PROGRAMMING",a.SPECIALITY_CHANNELS="SPECIALITY_CHANNELS",a.OFFERS="OFFERS",a.TV_BROWSE_ALL="TV_BROWSE_ALL",a.PROMOTION="PROMOTION",a.NONE="NONE",(i=e.EProductOfferingType||(e.EProductOfferingType={})).PACKAGE="BasePackage",i.COMBO="Combo",i.CHANNEL="Channel",i.NONE="None",(o=e.EProductOfferingGroupType||(e.EProductOfferingGroupType={})).Delta="Delta",o.New="New",o.Current="Current",o.Default="Default",(n=e.ELineOfBusiness||(e.ELineOfBusiness={})).TV="TV",n.Internet="Internet",(r=e.EAppointmentDuration||(e.EAppointmentDuration={})).AM="AM",r.PM="PM",r.Evening="Evening",r.AllDay="AllDay",r.Item0810="Item0810",r.Item1012="Item1012",r.Item1315="Item1315",r.Item1517="Item1517",r.Item1719="Item1719",r.Item1921="Item1921",(t=e.EPreferredContactMethod||(e.EPreferredContactMethod={})).EMAIL="Email",t.TEXT_MESSAGE="TextMessage",t.PHONE="Phone"}(Ie||(Ie={})),function(e){e.PREVIEWMODAL="PREVIEW_MODAL"}(ve||(ve={})),function(e){e[e.INIT=0]="INIT",e[e.RENDERED=1]="RENDERED",e[e.UPDATING=2]="UPDATING",e[e.ERROR=3]="ERROR",e[e.OUTAGERROR=4]="OUTAGERROR"}(Ne||(Ne={})),function(e){e.INTERNET="/Internet",e.TV="/TV",e.TV_Packages="/Packages",e.TV_MoviesSeries="/Movies",e.TV_Addons="/Addons",e.TV_Alacarte="/Alacarte",e.TV_International="/International",e.TV_InternationalCombos="/International/Combos",e.TV_InternationalAlacarte="/International/Alacarte",e.TV_Browse="/Browse",e.TV_Search="/Search",e.APPOINTMENT="/Appointment",e.REVIEW="/Review",e.CONFIRMATION="/Confirmation"}(Se||(Se={})),function(e){e.Summary="summary",e.Review="review",e.Confirmation="confirmation"}(Re||(Re={})),function(e){e.NAVIGATION="omf-changepackage-navigation",e.INTERNET="omf-changepackage-internet",e.TV="omf-changepackage-tv",e.APPOINTMENT="omf-changepackage-appointment",e.PREVIEW="omf-changepackage-review",e.REVIEW="omf-changepackage-review",e.CONFIRMATION="omf-changepackage-review"}(Ae||(Ae={})),function(e){e.INTERNET="Internet",e.TV="TV",e.ADDTV="AddTV",e.BUNDLE="Bundle"}(Pe||(Pe={})),function(e){var t,r;(r=e.EBrand||(e.EBrand={})).BELL="B",r.VIRGIN="V",r.LUCKY="L",t=function(e){function t(t,r,n){void 0===n&&(n=!1);var o=e.call(this,"object"==typeof t?t.message:t)||this;if(o.debug=!1,o.debug=n||Boolean(xe.getCookie("debugwidget")),"object"==typeof t&&(o.stack=t.stack),"object"==typeof r)switch(!0){case Boolean(r.url):o.type="API",o.response=r;break;case Boolean(r.componentStack):o.type="widget",o.componentStack=r.componentStack}else o.type="logic";return o}return u(t,e),t}(Error),e.ErrorHandler=t,e.ErrorHandlerObservable=function(e){return function(r,n){var o=r.error||r;return(0,re.merge)((0,re.of)(Z.errorOccured(new t(e.toString(),o))),n)}},e.noSpecialCharRegex=RegExp(/^[a-zA-Z0-9]+$/i),e.emailRegex=RegExp(/^[a-z0-9`!#\$%&\*\+\/=\?\^\'\-_]+((\.)+[a-z0-9`!#\$%&\*\+\/=\?\^\'\-_]+)*@([a-z0-9]+([\-][a-z0-9])*)+([\.]([a-z0-9]+([\-][a-z0-9])*)+)+$/i),e.phoneRegex=RegExp(/^[0-9]\d{2}-\d{3}-\d{4}$/i),e.hashCommaRegex=RegExp(/[\,\#]/i),e.onlyNumbersRegex=RegExp(/^[0-9]+$/i)}(je||(je={})),Ce=Boolean(xe.getCookie("debugwidget")),we=function(e){function t(t,r){var n=e.call(this,t)||this;return n.config=r,n}return u(t,e),Object.defineProperty(t.prototype,"useMockData",{get:function(){return Ce&&void 0!==this.config.mockdata||void 0!==this.config.mockdata&&this.config.environmentVariables.useMockData},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"mockdata",{get:function(){return this.config.mockdata||{}},enumerable:!1,configurable:!0}),t.prototype.get=function(t,r,n){var o=P(this.mockdata,D(t)+".GET",!1);return this.useMockData&&o?_(o):e.prototype.get.apply(this,arguments)},t.prototype.put=function(t,r,n){var o=P(this.mockdata,D(t)+".PUT",!1);return this.useMockData&&o?_(o):e.prototype.put.apply(this,arguments)},t.prototype.post=function(t,r,n){var o=P(this.mockdata,D(t)+".POST",!1);return this.useMockData&&o?_(o):e.prototype.post.apply(this,arguments)},t.prototype.del=function(t,r){var n=P(this.mockdata,D(t)+".DELETE",!1);return this.useMockData&&n?_(n):e.prototype.del.apply(this,arguments)},t.prototype.action=function(e){switch(e.method){case"PUT":return this.put(e.href,e.messageBody);case"POST":return this.post(e.href,e.messageBody);case"DELETE":return this.del(e.href);default:return this.get(e.href,e.messageBody)}},Object.defineProperty(t.prototype,"options",{get:function(){return{url:this.config.api.base,cache:!1,credentials:"include",headers:this.config.headers}},enumerable:!1,configurable:!0}),function(e,t,r,n){var o,i,a=arguments.length,c=a<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)c=Reflect.decorate(e,t,r,n);else for(i=e.length-1;i>=0;i--)(o=e[i])&&(c=(a<3?o(c):a>3?o(t,r,c):o(t,r))||c);return a>3&&c&&Object.defineProperty(t,r,c),c}([J.Injectable,p("design:paramtypes",[J.AjaxServices,Object])],t)}(J.CommonFeatures.BaseClient),_e=s(442),De=_e.createContext({}),ke=De.Provider,Le=De.Consumer,Me=s(769),Be=Z.setWidgetStatus,Ve=Z.showHideLoader,He=Z.errorOccured,Fe=Z.clearCachedState,Ge=function(){function e(){}return e.prototype.combineEpics=function(){return(0,Me.combineEpics)(this.onWidgetStatusEpic,this.onErrorOccuredEpic,this.clearCachedStateEpic)},Object.defineProperty(e.prototype,"onWidgetStatusEpic",{get:function(){return function(e){return e.pipe((0,Me.ofType)(Be.toString()),(0,re.mergeMap)(function(e){switch(e.payload){case Ne.INIT:case Ne.UPDATING:return[Ve(!0)];case Ne.RENDERED:case Ne.ERROR:return[Ve(!1)];default:return[]}}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onErrorOccuredEpic",{get:function(){return function(e){return e.pipe((0,Me.ofType)(He.toString()),(0,re.mergeMap)(function(e){return e.payload,[Be(Ne.ERROR)]}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"clearCachedStateEpic",{get:function(){return function(e){return e.pipe((0,Me.ofType)(Fe.toString()),(0,re.tap)(function(e){var t=e.payload;xe.clearCachedState(t)}),(0,re.mergeMap)(function(){return[]}))}},enumerable:!1,configurable:!0}),e}(),We=Z.raiseRestriction,Ue=Z.acceptRestriction,ze=Z.declineRestriction,$e=Z.finalizeRestriction,qe=Z.broadcastUpdate,Ye=Z.setWidgetStatus,Ke=Z.historyGo,Je=function(){function e(e,t){this.client=e,this.restrictionModalId=t}return e.prototype.combineEpics=function(){return(0,Me.combineEpics)(this.raiseRestrictionEpic,this.fadeRestrictionEpic,this.restrictionActionsEpic)},Object.defineProperty(e.prototype,"raiseRestrictionEpic",{get:function(){var e=this;return function(t){return t.pipe((0,Me.ofType)(We.toString()),(0,re.mergeMap)(function(){return xe.showLightbox(e.restrictionModalId||"RESTRICTIONS_MODAL"),re.EMPTY}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fadeRestrictionEpic",{get:function(){var e=this;return function(t){return t.pipe((0,Me.ofType)(Ue.toString(),ze.toString()),(0,re.mergeMap)(function(){return xe.hideLightbox(e.restrictionModalId||"RESTRICTIONS_MODAL"),re.EMPTY}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"restrictionActionsEpic",{get:function(){var e=this;return function(t){return t.pipe((0,Me.ofType)(Z.acceptRestriction.toString(),Z.declineRestriction.toString()),(0,re.filter)(function(e){var t=e.payload;return Boolean(t)}),(0,re.mergeMap)(function(t){var r=t.payload;return r.redirectURLKey?(0,re.of)(qe(Ke(r.redirectURLKey))):r.href?(0,re.concat)((0,re.of)(Ye(Ne.UPDATING)),e.client.action(r).pipe((0,re.mergeMap)(function(e){return w(e,[P(e,"data.productOfferingDetail.redirectURLKey",!1)?Ke(P(e,"data.productOfferingDetail.redirectURLKey")):$e(e.data)])}))):(0,re.of)()}),(0,re.catchError)(je.ErrorHandlerObservable(Z.acceptRestriction)))}},enumerable:!1,configurable:!0}),e}(),Xe=Z.openLightbox,Ze=Z.closeLightbox,Qe=Z.setlightboxData,et=function(){function e(){}return e.prototype.combineEpics=function(){return(0,Me.combineEpics)(this.onOpenLightboxEpic,this.closeLightbox)},Object.defineProperty(e.prototype,"onOpenLightboxEpic",{get:function(){return function(e){return e.pipe((0,Me.ofType)(Xe.toString()),(0,re.filter)(function(e){var t=e.payload;return!xe.isLightboxOpen("string"==typeof t?t:t.lightboxId)}),(0,re.tap)(function(e){var t=e.payload;return xe.showLightbox("string"==typeof t?t:t.lightboxId)}),(0,re.mergeMap)(function(e){var t=e.payload;return[Qe("string"==typeof t?t:t.data)]}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"closeLightbox",{get:function(){return function(e){return e.pipe((0,Me.ofType)(Ze.toString()),(0,re.filter)(function(e){var t=e.payload;return xe.isLightboxOpen("string"==typeof t?t:t.lightboxId)}),(0,re.tap)(function(e){var t=e.payload;return xe.hideLightbox("string"==typeof t?t:t.lightboxId)}))}},enumerable:!1,configurable:!0}),e}(),tt=(0,J.CommonFeatures.actionsToComputedPropertyName)(Z),rt=tt.setWidgetStatus,nt=tt.errorOccured,ot=(0,J.CommonFeatures.actionsToComputedPropertyName)(Z),it=ot.raiseRestriction,at=ot.acceptRestriction,ct=ot.declineRestriction,st=(0,J.CommonFeatures.actionsToComputedPropertyName)(Z),ut=st.closeLightbox,lt=st.setlightboxData,function(e){e.WidgetBaseLifecycle=L,e.WidgetRestrictions=M,e.WidgetLightboxes=B}(pt||(pt={})),dt=function(e){var t=e.className,r=e.children;return(0,Ee.jsx)("div",{className:"brf3-panel ".concat(t),children:r})},dt.defaultProps={className:""},ft=function(e){var t=e.className,r=e.children;return(0,Ee.jsx)("div",{className:"brf3-container ".concat(t),children:r})},ft.defaultProps={className:""},ht=function(e){var t=e.className,r=e.children;return(0,Ee.jsx)("div",{className:"container liquid-container ".concat(t),children:r})},ht.defaultProps={className:""},function(e){var t,r,n,o,i;e.Ref=function(e){return{ref:e}},(i=e.EMessageType||(e.EMessageType={})).Confirmation="C",i.Information="I",i.Warning="W",i.Error="E",(o=e.EErrorType||(e.EErrorType={})).Technical="T",o.Business="B",o.Validation="V",(n=e.EApplicationLayer||(e.EApplicationLayer={})).Browser="BR",n.Frontend="FE",n.ESB="ESB",n.Backend="BE",n.Servicegrid="SG",n.Cache="C",e.Component=function(e){var t=e.children,r=l(e,["children"]),n=_e.Children.toArray(t);if(n.length>1)throw"Omniture component may not have more then one child";return n.length>0?(0,Ee.jsx)(Ee.Fragment,{children:n.map(function(e){return _e.cloneElement(e,ee(ee({},e.props),{"data-omni":btoa(JSON.stringify(r))}))})}):(0,Ee.jsx)("span",{"data-omni":btoa(JSON.stringify(r))})},r={trackPage:t=function(){},trackFragment:t,trackAction:t,trackError:t,trackFailure:t,updateContext:t},e.useOmniture=function(){return window.OmnitureTracker&&window.OmnitureTracker.getInstance()||r}}(mt||(mt={})),gt=function(e){var t=e.details;return _e.useEffect(function(){var e=0;switch(xe.getFlowType()){case Pe.INTERNET:e=543;break;case Pe.TV:e=394}switch(t.response&&t.response.url.includes("ProductOrder/Summary")&&(e=104),t.type){case"API":mt.useOmniture().trackError({code:"API"+P(t,"response.status","500"),type:mt.EErrorType.Technical,layer:mt.EApplicationLayer.Backend,description:{ref:"TechnicalErrorMessage"},ajax:!0},e);break;case"widget":mt.useOmniture().trackError({code:"WIDGET400",type:mt.EErrorType.Technical,layer:mt.EApplicationLayer.Frontend,description:{ref:"TechnicalErrorMessage"}},e);break;default:mt.useOmniture().trackError({code:"LOGIC500",type:mt.EErrorType.Technical,layer:mt.EApplicationLayer.Frontend,description:{ref:"TechnicalErrorMessage"}},e)}},[]),(0,Ee.jsxs)(ht,{className:"error margin-30-bottom",children:[(0,Ee.jsx)("div",{className:"spacer30","aria-hidden":"true"}),(0,Ee.jsxs)(dt,{className:"border bgWgite borderGray4 pad-30",children:[(0,Ee.jsxs)("div",{className:"row",children:[(0,Ee.jsx)("div",{className:"inlineBlock icon-width-40 valign-middle text-center-xs",children:(0,Ee.jsx)("span",{className:"txtRed txtSize32 icons icons-info"})}),(0,Ee.jsx)("div",{className:"spacer15","aria-hidden":"true"}),(0,Ee.jsx)("div",{className:"inlineBlock pad-20-left no-pad-left-xs content-width valign-middle",children:(0,Ee.jsx)("span",{className:"txtBlack2 block txtSize20",id:"TechnicalErrorMessage",children:(0,Ee.jsx)(Oe.FormattedMessage,{id:"TECHNICAL_ERROR"})})})]}),(0,Ee.jsxs)("div",{className:"margin-20-top",style:{display:t.debug?"block":"none"},children:[function(e){switch(e){case"API":return(0,Ee.jsxs)(_e.Fragment,{children:[(0,Ee.jsxs)("p",{className:"margin-10-top",children:["API Request failed ",P(t,"response.status","unknown")," (",P(t,"response.statusText","unknown"),")"]}),(0,Ee.jsxs)("p",{className:"margin-10-top",style:{wordBreak:"break-all"},children:["URL: ",P(t,"response.url","unknown")]}),(0,Ee.jsxs)("p",{className:"margin-10-top",style:{wordBreak:"break-all"},children:["Response: ",JSON.stringify(P(t,"response.data","Null"),null," ")]})]});case"widget":return(0,Ee.jsxs)(_e.Fragment,{children:[(0,Ee.jsx)("p",{className:"margin-10-top",children:"Widget render failed"}),(0,Ee.jsxs)("p",{className:"margin-10-top",children:["Component: ",(0,Ee.jsx)("pre",{children:t.componentStack})]})]});default:return(0,Ee.jsx)("p",{className:"margin-10-top",children:"General logic falure"})}}(t.type),(0,Ee.jsxs)("p",{className:"margin-10-top",children:["Stack trace: ",(0,Ee.jsx)("pre",{children:JSON.stringify(P(t,"stack"),null," ")})]})]})]})]})},yt=s(999),bt=function(e){return("boolean"==typeof e.when?e.when:Boolean(e.when))?e.children:e.placeholder},function(e){e.SHOW="show.bs.modal",e.SHOWN="shown.bs.modal",e.HIDE="hide.bs.modal",e.HIDDEN="hidden.bs.modal"}(Et||(Et={})),Ot=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return u(t,e),t.prototype.componentDidMount=function(){var e=this;this.props.onShow&&$("#".concat(this.props.id||this.props.modalId)).on(Et.SHOW,this.props.onShow),this.props.onShown&&$("#".concat(this.props.id||this.props.modalId)).on(Et.SHOWN,this.props.onShown),this.props.onHide&&$("#".concat(this.props.id||this.props.modalId)).on(Et.HIDE,this.props.onHide),this.props.onHidden&&$("#".concat(this.props.id||this.props.modalId)).on(Et.HIDDEN,this.props.onHidden),this.onClose=this.onClose.bind(this),$("#".concat(this.props.id||this.props.modalId)).on(Et.HIDDEN,function(){var t=e.props.lightboxData&&e.props.lightboxData.relativeId&&document.getElementById(e.props.lightboxData.relativeId);t&&t.focus(),e.props.clearLightboxData()})},t.prototype.onClose=function(){xe.isLightboxOpen(this.props.id||this.props.modalId)&&(this.props.onCloseLightbox(this.props.id||this.props.modalId),void 0!==this.props.onClose&&this.props.onClose(this.props.id||this.props.modalId))},t.prototype.render=function(){var e=this.props,t=e.id,r=e.className,n=void 0===r?"":r,o=e.size,i=e.title,a=e.containerClass,c=void 0===a?[]:a,s=e.modalId,u=e.children,l=e.onDismiss,p=e.permanent;return(0,Ee.jsxs)("div",{id:t||s,className:"modal modal-vm fade ".concat(n),role:"dialog",tabIndex:-1,"data-backdrop":"static","data-keyboard":"false","aria-modal":"true","aria-labelledby":"".concat(t||s,"_label"),"aria-hidden":"true",children:[(0,Ee.jsx)("span",{className:"sr-only",children:"dialog"}),(0,Ee.jsx)("div",{className:"modal-dialog modal-md modal-bg modal-".concat(o," bell-modal-").concat(o),role:"document",children:(0,Ee.jsxs)("div",{className:"modal-content noBorderRadius noBorder-xs",children:[(0,Ee.jsxs)("div",{className:"modal-header bgGrayLight2 pad-30-left pad-30-right pad-25-top pad-25-bottom pad-15-left-right-xs align-items-center noBorderRadius accss-focus-outline-override-grey-bg",children:[(0,Ee.jsx)("h2",{id:"".concat(t||s,"_label"),className:"virginUltra txtBlack txtSize24 overflow-ellipsis txtSize18-xs txtUppercase sans-serif-xs lineHeight1_5 margin-b-0",children:i}),(0,Ee.jsx)(bt,{when:!p,children:(0,Ee.jsx)("button",{onClick:l,id:"close_".concat(t||s),type:"button",className:"no-pad close","data-dismiss":"modal","aria-label":"Close Dialog","aria-describedby":"".concat(t||s,"_label"),autoFocus:!0,children:(0,Ee.jsx)("span",{className:"volt-icon icon-big_X"})})})]}),(0,Ee.jsx)("div",{id:"".concat(t||s,"_desc"),className:"modal-body pad-0 ".concat(c.join(" ")),children:u})]})})]})},t.prototype.componentWillUnmount=function(){this.props.onShow&&$("#".concat(this.props.id||this.props.modalId)).off(Et.SHOW,this.props.onShow),this.props.onShown&&$("#".concat(this.props.id||this.props.modalId)).off(Et.SHOWN,this.props.onShown),this.props.onHide&&$("#".concat(this.props.id||this.props.modalId)).off(Et.HIDE,this.props.onHide),this.props.onHidden&&$("#".concat(this.props.id||this.props.modalId)).off(Et.HIDDEN,this.props.onHidden)},t.defaultProps={className:"",size:"md"},t}(_e.Component),Tt=(0,yt.connect)(function(e){return{lightboxData:e.lightboxData}},function(e){return{onCloseLightbox:function(t){return e(Z.closeLightbox(t))},clearLightboxData:function(){return e(Z.setlightboxData(""))}}})(Ot),xt="",It=function(e){var t=e.id,r=e.type,n=e.title,o=e.description,i=e.dynamicData,a=e.footerDescription,c=e.actionLinks,s=e.onDismiss,u=e.onAction,l=e.onComplete;return xt=r,(0,Ee.jsxs)(Tt,{modalId:t||"RESTRICTIONS_MODAL",permanent:!0,onClose:function(){s(),l&&l("close")},onShown:function(){return function(e){var t,r={actionId:104,actionresult:0,applicationState:0};switch(xt){case"Warning":case"Error":r.actionresult=2,r.applicationState=2,t=mt.EMessageType.Warning;break;default:t=mt.EMessageType.Information}mt.useOmniture().trackFragment({id:"restrictionLightbox",s_oAPT:r,s_oPRM:{ref:"".concat(e,"_label")},s_oLBC:{ref:"".concat(e,"_description")},s_oPLE:{content:{ref:"".concat(e,"_description")},type:t}})}(t)},title:n,children:[(0,Ee.jsx)("div",{className:"modal-body bgWhite",children:(0,Ee.jsxs)("div",{className:"flex",children:[H(r||""),(0,Ee.jsxs)("div",{id:"".concat(t,"_description"),children:[(0,Ee.jsx)("p",{dangerouslySetInnerHTML:{__html:o}}),(0,Ee.jsx)(bt,{when:P(i,"productList.length",!1),children:(0,Ee.jsx)("ul",{children:P(i,"productList",[]).map(function(e){return(0,Ee.jsx)("li",{className:"txtBold txtBlack",children:e})})})}),(0,Ee.jsx)(bt,{when:P(i,"promotion.length",!1),children:(0,Ee.jsx)("ul",{children:P(i,"promotion",[]).map(function(e){return(0,Ee.jsxs)("li",{children:[(0,Ee.jsx)("span",{className:"txtBold txtBlack",children:e.promoName}),(0,Ee.jsx)("br",{}),(0,Ee.jsx)(bt,{when:Boolean(e.promoExpiry),children:(0,Ee.jsx)(Oe.FormattedDate,{value:e.promoExpiry,format:"yMMMd",timeZone:"UTC",children:function(e){return(0,Ee.jsx)(Oe.FormattedMessage,{id:"PromotionExpires",values:{expiryDate:e}})}})})]})})})}),(0,Ee.jsx)(bt,{when:Boolean(a),children:(0,Ee.jsx)("p",{className:"txtBold",dangerouslySetInnerHTML:{__html:a}})})]})]})}),(0,Ee.jsxs)(bt,{when:Boolean(c&&c.length>0),children:[(0,Ee.jsx)("div",{className:"spacer1 bgGrayLight6","aria-hidden":"true"}),(0,Ee.jsx)("div",{className:"bgGray19 pad-30 pad-15-left-right-xs",children:P(c,void 0,[]).map(function(e,t){return(0,Ee.jsxs)(_e.Fragment,{children:[(0,Ee.jsx)("button",{id:"ACTION_SUBMIT",className:V(t),onClick:function(){u(e),l&&l(e.rel)},children:e.name}),(0,Ee.jsx)("div",{className:"vSpacer15","aria-hidden":"true"})]})})})]})]})},vt=(0,yt.connect)(function(e){var t=e.restriction;return t?ee({},t):{}},function(e){return{onAction:function(t){mt.useOmniture().trackAction({id:"restrictionLightbox",s_oAPT:{actionId:647,actionresult:0,applicationState:0},s_oBTN:t.name}),"Cancel"===t.name?e(Z.declineRestriction(t)):e(Z.acceptRestriction(t))},onDismiss:function(){return e(Z.declineRestriction())}}})(It),(Nt=function(e){var t=e.text,r=e.maxLength,n=e.className;return(0,Ee.jsx)("p",{className:"ellipsis-text ".concat(n),children:t.length<=r?t:"".concat(t.substring(0,r),"...")})}).defaultProps={className:""},Nt.displayName="EllipsisText",St=function(e){return Boolean(e.localization&&e.localization.messages&&Object.keys(e.localization.messages).length)?(0,Ee.jsx)(Oe.IntlProvider,ee({},e.localization,{formats:ee(ee({},e.localization.formats),{number:{CAD:{currency:"CAD",currencyDisplay:"symbol",style:"currency",minimumFractionDigits:2}}}),locale:e.localization.fullLocale,children:(0,Ee.jsx)(_e.Fragment,{children:function(t){switch(t){case Ne.RENDERED:case Ne.UPDATING:return e.children;case Ne.ERROR:return(0,Ee.jsx)(gt,{details:e.errorHandlerProps});default:return e.placeholder}}(e.widgetStatus)})})):null},Rt=(0,yt.connect)(function(e){var t=e.localization,r=e.error;return{localization:t,widgetStatus:e.widgetStatus,errorHandlerProps:r}},{},function(e,t,r){return ee(ee(ee({},r.propsfilter?r.propsfilter(e):e),t),r)})(St),At=function(e){var t=e.value,r=e.className,n=e.localization,o=e.tag,i=e.tagProps,a=(e.credit,o||"span");return(0,Ee.jsx)(a,ee({},i,{className:"txtCurrency ".concat(r||""),dangerouslySetInnerHTML:{__html:G(n,t)}}))};const Bt=At;return Pt=(0,yt.connect)(function(e){return{localization:e.localization}},function(e){return{}})(Bt),Pt.displayName="BellCurrency",jt=function(e){var t=e.id,r=l(e,["id"]);return(0,Ee.jsx)(Le,{children:function(e){var n=e.config;return(0,Ee.jsx)(Oe.FormattedMessage,ee({},r,{id:"".concat(n.environmentVariables.brand,"_").concat(t)}))}})},Ct=function(e){var t,r=e.str,n=e.prefixClassName,o=e.fractionClassName,i="",a="",c="";return 0===r.indexOf("$")?(c=(t=r.split("."))[0].substr(0,1),i=t[0].substr(1),a=t[1]):(i=(t=r.split(","))[0],a=t[1]),(0,Ee.jsxs)(Ee.Fragment,{children:[Boolean(c)?(0,Ee.jsx)("sup",{className:n,children:c}):null,i,(0,Ee.jsx)("sup",{className:o,"aria-hidden":!0,children:a}),"00"!==a&&(0,Ee.jsxs)("span",{className:"sr-only",children:[".",a," cents"]})]})},wt=function(e){var t=e.className,r=e.prefixClassName,n=e.fractionClassName,o=e.value,i=e.monthly;return(0,Ee.jsx)(Oe.FormattedNumber,{value:o,format:"CAD",children:function(e){return(0,Ee.jsxs)("span",{className:"formatted-currency ".concat(t),children:[(0,Ee.jsx)(Ct,{str:e,prefixClassName:r,fractionClassName:n}),i?(0,Ee.jsxs)("sup",{className:r,children:[(0,Ee.jsx)(Oe.FormattedMessage,{id:"PER_MO",children:function(e){return(0,Ee.jsx)("span",{"aria-hidden":"true",children:e})}}),(0,Ee.jsx)(Oe.FormattedMessage,{id:"PER_MONTH",children:function(e){return(0,Ee.jsx)("span",{className:"sr-only",children:e})}})]}):null]})}})},wt.defaultProps={className:"",prefixClassName:"txtSize22",fractionClassName:"txtSize22"},_t=function(e){function t(){var e,r,n,o,i;for(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];return r=function(e,t){return!t||"object"!==W(t)&&"function"!=typeof t?z(e):t}(this,(e=U(t)).call.apply(e,[this].concat(o))),Y(z(r),"state",{bootstrapped:!1}),Y(z(r),"_unsubscribe",void 0),Y(z(r),"handlePersistorState",function(){r.props.persistor.getState().bootstrapped&&(r.props.onBeforeLift?Promise.resolve(r.props.onBeforeLift()).finally(function(){return r.setState({bootstrapped:!0})}):r.setState({bootstrapped:!0}),r._unsubscribe&&r._unsubscribe())}),r}var r;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&q(e,t)}(t,e),(r=[{key:"componentDidMount",value:function(){this._unsubscribe=this.props.persistor.subscribe(this.handlePersistorState),this.handlePersistorState()}},{key:"componentWillUnmount",value:function(){this._unsubscribe&&this._unsubscribe()}},{key:"render",value:function(){return"function"==typeof this.props.children?this.props.children(this.state.bootstrapped):this.state.bootstrapped?this.props.children:this.props.loading}}])&&function(e,t){var r,n;for(r=0;r<t.length;r++)(n=t[r]).enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}(t.prototype,r),t}(_e.PureComponent),Y(_t,"defaultProps",{children:null,loading:null}),Dt=function(e){return(0,Ee.jsx)(_t,{loading:null,persistor:(t=e.store,r=!1,n=(0,de.createStore)(he,fe,void 0),o=function(e){n.dispatch({type:ue,key:e})},i=function(e,o,i){var c={type:ie,payload:o,err:i,key:e};t.dispatch(c),n.dispatch(c),r&&a.getState().bootstrapped&&(r(),r=!1)},a=R({},n,{purge:function(){var e=[];return t.dispatch({type:se,result:function(t){e.push(t)}}),Promise.all(e)},flush:function(){var e=[];return t.dispatch({type:oe,result:function(t){e.push(t)}}),Promise.all(e)},pause:function(){t.dispatch({type:ae})},persist:function(){t.dispatch({type:ce,register:o,rehydrate:i})}}),a.persist(),a),children:e.render()});var t,r,n,o,i,a},function(e){e.Error=gt,e.Container=ht,e.Panel=dt,e.BRF3Container=ft,e.Modal=Tt,e.RestrictionModal=vt,e.ApplicationRoot=Rt,e.EllipsisText=Nt,e.Currency=wt,e.BellCurrency=Pt,e.BrandedMessage=jt,e.PersistGate=Dt,e.Visible=bt}(kt||(kt={})),K})(),"object"==typeof exports&&"object"==typeof module?module.exports=factory(require("bwtk"),require("redux-actions"),require("rxjs"),require("redux"),require("react-intl"),require("react"),require("redux-observable"),require("react-redux")):"function"==typeof define&&define.amd?define("omf-changepackage-components",["bwtk","redux-actions","rxjs","redux","react-intl","react","redux-observable","react-redux"],factory):"object"==typeof exports?exports["omf-changepackage-components"]=factory(require("bwtk"),require("redux-actions"),require("rxjs"),require("redux"),require("react-intl"),require("react"),require("redux-observable"),require("react-redux")):root["omf-changepackage-components"]=factory(root.bwtk,root.ReduxActions,root.rxjs,root.Redux,root.ReactIntl,root.React,root.ReduxObservable,root.ReactRedux);